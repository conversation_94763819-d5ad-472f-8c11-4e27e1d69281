[project]
name = "click"
version = "8.2.1"
description = "Composable command line interface toolkit"
readme = "README.md"
license = "BSD-3-Clause"
license-files = ["LICENSE.txt"]
maintainers = [{name = "Palle<PERSON>", email = "<EMAIL>"}]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Typing :: Typed",
]
requires-python = ">=3.10"
dependencies = [
    "colorama; platform_system == 'Windows'",
]

[project.urls]
Donate = "https://palletsprojects.com/donate"
Documentation = "https://click.palletsprojects.com/"
Changes = "https://click.palletsprojects.com/page/changes/"
Source = "https://github.com/pallets/click/"
Chat = "https://discord.gg/pallets"

[dependency-groups]
dev = [
    "ruff",
    "tox",
    "tox-uv",
]
docs = [
    "myst-parser",
    "pallets-sphinx-themes",
    "sphinx",
    "sphinx-tabs",
    "sphinxcontrib-log-cabinet",
]
docs-auto = [
    "sphinx-autobuild",
]
gha-update = [
    "gha-update ; python_full_version >= '3.12'",
]
pre-commit = [
    "pre-commit",
    "pre-commit-uv",
]
tests = [
    "pytest",
]
typing = [
    "mypy",
    "pyright",
    "pytest",
]

[build-system]
requires = ["flit_core<4"]
build-backend = "flit_core.buildapi"

[tool.flit.module]
name = "click"

[tool.flit.sdist]
include = [
    "docs/",
    "tests/",
    "CHANGES.rst",
    "uv.lock"
]
exclude = [
    "docs/_build/",
]

[tool.uv]
default-groups = ["dev", "pre-commit", "tests", "typing"]

[tool.pytest.ini_options]
testpaths = ["tests"]
filterwarnings = [
    "error",
]

[tool.coverage.run]
branch = true
source = ["click", "tests"]

[tool.coverage.paths]
source = ["src", "*/site-packages"]

[tool.coverage.report]
exclude_also = [
    "if t.TYPE_CHECKING",
    "raise NotImplementedError",
    ": \\.{3}",
]

[tool.mypy]
python_version = "3.10"
files = ["src", "tests/typing"]
show_error_codes = true
pretty = true
strict = true

[[tool.mypy.overrides]]
module = [
    "colorama.*",
]
ignore_missing_imports = true

[tool.pyright]
pythonVersion = "3.10"
include = ["src", "tests/typing"]
typeCheckingMode = "basic"

[tool.ruff]
extend-exclude = ["examples/"]
src = ["src"]
fix = true
show-fixes = true
output-format = "full"

[tool.ruff.lint]
select = [
    "B",  # flake8-bugbear
    "E",  # pycodestyle error
    "F",  # pyflakes
    "I",  # isort
    "UP",  # pyupgrade
    "W",  # pycodestyle warning
]
ignore = [
    "UP038",  # keep isinstance tuple
]

[tool.ruff.lint.isort]
force-single-line = true
order-by-type = false

[tool.tox]
env_list = [
    "py3.13", "py3.12", "py3.11", "py3.10",
    "pypy3.11",
    "style",
    "typing",
    "docs",
]

[tool.tox.env_run_base]
description = "pytest on latest dependency versions"
runner = "uv-venv-lock-runner"
package = "wheel"
wheel_build_env = ".pkg"
constrain_package_deps = true
use_frozen_constraints = true
dependency_groups = ["tests"]
commands = [[
    "pytest", "-v", "--tb=short", "--basetemp={env_tmp_dir}",
    {replace = "posargs", default = [], extend = true},
]]

[tool.tox.env.style]
description = "run all pre-commit hooks on all files"
dependency_groups = ["pre-commit"]
skip_install = true
commands = [["pre-commit", "run", "--all-files"]]

[tool.tox.env.typing]
description = "run static type checkers"
dependency_groups = ["typing"]
commands = [
    ["mypy"],
]

[tool.tox.env.docs]
description = "build docs"
dependency_groups = ["docs"]
commands = [["sphinx-build", "-E", "-W", "-b", "dirhtml", "docs", "docs/_build/dirhtml"]]

[tool.tox.env.docs-auto]
description = "continuously rebuild docs and start a local server"
dependency_groups = ["docs", "docs-auto"]
commands = [["sphinx-autobuild", "-W", "-b", "dirhtml", "--watch", "src", "docs", "docs/_build/dirhtml"]]

[tool.tox.env.update-actions]
description = "update GitHub Actions pins"
labels = ["update"]
dependency_groups = ["gha-update"]
skip_install = true
commands = [["gha-update"]]

[tool.tox.env.update-pre_commit]
description = "update pre-commit pins"
labels = ["update"]
dependency_groups = ["pre-commit"]
skip_install = true
commands = [["pre-commit", "autoupdate", "--freeze", "-j4"]]

[tool.tox.env.update-requirements]
description = "update uv lock"
labels = ["update"]
dependency_groups = []
no_default_groups = true
skip_install = true
commands = [["uv", "lock", {replace = "posargs", default = ["-U"], extend = true}]]
