$ aliases_

  aliases is a fairly advanced example that shows how
  to implement command aliases with Click.  It uses a
  subclass of the default group to customize how commands
  are located.

  It supports both aliases read from a config file as well
  as automatic abbreviations.

  The aliases from the config are read from the aliases.ini
  file.  Try `aliases st` and `aliases ci`!

Usage:

  $ pip install --editable .
  $ aliases --help
