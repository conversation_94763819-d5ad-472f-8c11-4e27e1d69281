"""The simple example from https://github.com/pallets/click#a-simple-example."""

from typing_extensions import assert_type

import click


@click.command()
@click.option("--count", default=1, help="Number of greetings.")
@click.option("--name", prompt="Your name", help="The person to greet.")
def hello(count: int, name: str) -> None:
    """Simple program that greets NAME for a total of COUNT times."""
    for _ in range(count):
        click.echo(f"Hello, {name}!")


assert_type(hello, click.Command)
