["tests/test_arguments.py::test_argument_unbounded_nargs_cant_have_default", "tests/test_arguments.py::test_bytes_args", "tests/test_arguments.py::test_defaults_for_nargs", "tests/test_arguments.py::test_deprecated_required", "tests/test_arguments.py::test_deprecated_usage", "tests/test_arguments.py::test_deprecated_warning[True]", "tests/test_arguments.py::test_deprecated_warning[USE B INSTEAD]", "tests/test_arguments.py::test_duplicate_names_warning[args_one0-args_two0]", "tests/test_arguments.py::test_eat_options", "tests/test_arguments.py::test_empty_nargs", "tests/test_arguments.py::test_file_args", "tests/test_arguments.py::test_file_atomics", "tests/test_arguments.py::test_implicit_non_required", "tests/test_arguments.py::test_missing_arg", "tests/test_arguments.py::test_missing_argument_string_cast", "tests/test_arguments.py::test_multiple_not_allowed", "tests/test_arguments.py::test_multiple_param_decls_not_allowed", "tests/test_arguments.py::test_nargs_bad_default[value0]", "tests/test_arguments.py::test_nargs_bad_default[value1]", "tests/test_arguments.py::test_nargs_bad_default[value2]", "tests/test_arguments.py::test_nargs_envvar[-1--expect5]", "tests/test_arguments.py::test_nargs_envvar[-1-a b c-expect4]", "tests/test_arguments.py::test_nargs_envvar[2--None]", "tests/test_arguments.py::test_nargs_envvar[2-a b c-Takes 2 values but 3 were given.]", "tests/test_arguments.py::test_nargs_envvar[2-a b-expect2]", "tests/test_arguments.py::test_nargs_envvar[2-a-Takes 2 values but 1 was given.]", "tests/test_arguments.py::test_nargs_envvar_only_if_values_empty", "tests/test_arguments.py::test_nargs_err", "tests/test_arguments.py::test_nargs_specified_plus_star_ordering", "tests/test_arguments.py::test_nargs_star", "tests/test_arguments.py::test_nargs_star_ordering", "tests/test_arguments.py::test_nargs_tup", "tests/test_arguments.py::test_nargs_tup_composite[opts0]", "tests/test_arguments.py::test_nargs_tup_composite[opts1]", "tests/test_arguments.py::test_nargs_tup_composite[opts2]", "tests/test_arguments.py::test_nargs_tup_composite[opts3]", "tests/test_arguments.py::test_nested_subcommand_help", "tests/test_arguments.py::test_path_allow_dash", "tests/test_arguments.py::test_stdout_default", "tests/test_arguments.py::test_subcommand_help", "tests/test_arguments.py::test_when_argument_decorator_is_used_multiple_times_cls_is_preserved", "tests/test_basic.py::test_basic_functionality", "tests/test_basic.py::test_basic_group", "tests/test_basic.py::test_boolean_conversion[0-False]", "tests/test_basic.py::test_boolean_conversion[1-True]", "tests/test_basic.py::test_boolean_conversion[f-False]", "tests/test_basic.py::test_boolean_conversion[false-False]", "tests/test_basic.py::test_boolean_conversion[n-False]", "tests/test_basic.py::test_boolean_conversion[no-False]", "tests/test_basic.py::test_boolean_conversion[off-False]", "tests/test_basic.py::test_boolean_conversion[on-True]", "tests/test_basic.py::test_boolean_conversion[t-True]", "tests/test_basic.py::test_boolean_conversion[true-True]", "tests/test_basic.py::test_boolean_conversion[y-True]", "tests/test_basic.py::test_boolean_conversion[yes-True]", "tests/test_basic.py::test_boolean_flag[False-args2-True]", "tests/test_basic.py::test_boolean_flag[False-args3-False]", "tests/test_basic.py::test_boolean_flag[True-args0-True]", "tests/test_basic.py::test_boolean_flag[True-args1-True]", "tests/test_basic.py::test_boolean_switch[args0-True-False]", "tests/test_basic.py::test_boolean_switch[args0-True-True]", "tests/test_basic.py::test_boolean_switch[args1-False-False]", "tests/test_basic.py::test_boolean_switch[args1-False-True]", "tests/test_basic.py::test_boolean_switch[args2-None-False]", "tests/test_basic.py::test_boolean_switch[args2-None-True]", "tests/test_basic.py::test_choice_argument", "tests/test_basic.py::test_choice_argument_custom_type", "tests/test_basic.py::test_choice_argument_enum", "tests/test_basic.py::test_choice_argument_none", "tests/test_basic.py::test_choice_option", "tests/test_basic.py::test_datetime_option_custom", "tests/test_basic.py::test_datetime_option_default", "tests/test_basic.py::test_evaluation_order", "tests/test_basic.py::test_file_lazy_mode", "tests/test_basic.py::test_file_option", "tests/test_basic.py::test_float_option[--f=23.5-F:[23.5]]", "tests/test_basic.py::test_float_option[--f=x-Error: Invalid value for '--f': 'x' is not a valid float.]", "tests/test_basic.py::test_float_option[args0-F:[42.0]]", "tests/test_basic.py::test_group_commands_dict", "tests/test_basic.py::test_group_from_list", "tests/test_basic.py::test_help_invalid_default", "tests/test_basic.py::test_hidden_command", "tests/test_basic.py::test_hidden_group", "tests/test_basic.py::test_hidden_option", "tests/test_basic.py::test_int_option[args0-I:[84]]", "tests/test_basic.py::test_int_option[args1-I:[46]]", "tests/test_basic.py::test_int_option[args2-Error: Invalid value for '--i': 'x' is not a valid integer.]", "tests/test_basic.py::test_path_option", "tests/test_basic.py::test_repr", "tests/test_basic.py::test_required_option", "tests/test_basic.py::test_return_values", "tests/test_basic.py::test_string_option[args0-S:[no value]]", "tests/test_basic.py::test_string_option[args1-S:[42]]", "tests/test_basic.py::test_string_option[args2-Error: Option '--s' requires an argument.]", "tests/test_basic.py::test_string_option[args3-S:[]]", "tests/test_basic.py::test_string_option[args4-S:[\\u2603]]", "tests/test_basic.py::test_summary_line", "tests/test_basic.py::test_uuid_option[args0-U:[ba122011-349f-423b-873b-9d6a79c688ab]]", "tests/test_basic.py::test_uuid_option[args1-U:[821592c1-c50e-4971-9cd6-e89dc6832f86]]", "tests/test_basic.py::test_uuid_option[args2-Error: Invalid value for '--u': 'x' is not a valid UUID.]", "tests/test_chain.py::test_args_and_chain", "tests/test_chain.py::test_basic_chaining", "tests/test_chain.py::test_chaining_help[args0-COMMAND1 [ARGS]... [COMMAND2 [ARGS]...]...]", "tests/test_chain.py::test_chaining_help[args1-ROOT HELP]", "tests/test_chain.py::test_chaining_help[args2-SDIST HELP]", "tests/test_chain.py::test_chaining_help[args3-BDIST HELP]", "tests/test_chain.py::test_chaining_help[args4-SDIST HELP]", "tests/test_chain.py::test_chaining_with_arguments", "tests/test_chain.py::test_chaining_with_options", "tests/test_chain.py::test_group_arg_behavior", "tests/test_chain.py::test_group_chaining", "tests/test_chain.py::test_no_command_result_callback[False-1]", "tests/test_chain.py::test_no_command_result_callback[True-[]]", "tests/test_chain.py::test_pipeline[args0-foo\\nbar-expect0]", "tests/test_chain.py::test_pipeline[args1-foo \\n bar-expect1]", "tests/test_chain.py::test_pipeline[args2-foo \\n bar-expect2]", "tests/test_command_decorators.py::test_command_no_parens", "tests/test_command_decorators.py::test_custom_command_no_parens", "tests/test_command_decorators.py::test_generate_name[init_data]", "tests/test_command_decorators.py::test_generate_name[init_data_cmd]", "tests/test_command_decorators.py::test_generate_name[init_data_command]", "tests/test_command_decorators.py::test_generate_name[init_data_group]", "tests/test_command_decorators.py::test_generate_name[init_data_grp]", "tests/test_command_decorators.py::test_group_no_parens", "tests/test_command_decorators.py::test_params_argument", "tests/test_commands.py::test_abort_exceptions_with_disabled_standalone_mode[EOFError]", "tests/test_commands.py::test_abort_exceptions_with_disabled_standalone_mode[KeyboardInterrupt]", "tests/test_commands.py::test_aliased_command_canonical_name", "tests/test_commands.py::test_auto_shorthelp", "tests/test_commands.py::test_command_no_args_is_help", "tests/test_commands.py::test_command_parse_args_collects_option_prefixes", "tests/test_commands.py::test_custom_parser", "tests/test_commands.py::test_default_maps", "tests/test_commands.py::test_deprecated_in_help_messages[True-CLI HELP]", "tests/test_commands.py::test_deprecated_in_help_messages[True-None]", "tests/test_commands.py::test_deprecated_in_help_messages[USE OTHER COMMAND INSTEAD-CLI HELP]", "tests/test_commands.py::test_deprecated_in_help_messages[USE OTHER COMMAND INSTEAD-None]", "tests/test_commands.py::test_deprecated_in_invocation[True]", "tests/test_commands.py::test_deprecated_in_invocation[USE OTHER COMMAND INSTEAD]", "tests/test_commands.py::test_forwarded_params_consistency", "tests/test_commands.py::test_group_add_command_name", "tests/test_commands.py::test_group_invoke_collects_used_option_prefixes", "tests/test_commands.py::test_group_parse_args_collects_base_option_prefixes", "tests/test_commands.py::test_group_with_args[args0-2-Error: Missing command.]", "tests/test_commands.py::test_group_with_args[args1-0-Show this message and exit.]", "tests/test_commands.py::test_group_with_args[args2-0-obj=obj1\\nmove\\n]", "tests/test_commands.py::test_group_with_args[args3-2-Show this message and exit.]", "tests/test_commands.py::test_help_param_priority", "tests/test_commands.py::test_invoked_subcommand", "tests/test_commands.py::test_iter_params_for_processing[invocation_order0-declaration_order0-expected_order0]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order1-declaration_order1-expected_order1]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order10-declaration_order10-expected_order10]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order11-declaration_order11-expected_order11]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order12-declaration_order12-expected_order12]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order13-declaration_order13-expected_order13]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order14-declaration_order14-expected_order14]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order15-declaration_order15-expected_order15]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order16-declaration_order16-expected_order16]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order17-declaration_order17-expected_order17]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order18-declaration_order18-expected_order18]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order19-declaration_order19-expected_order19]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order2-declaration_order2-expected_order2]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order20-declaration_order20-expected_order20]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order21-declaration_order21-expected_order21]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order22-declaration_order22-expected_order22]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order23-declaration_order23-expected_order23]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order24-declaration_order24-expected_order24]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order25-declaration_order25-expected_order25]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order26-declaration_order26-expected_order26]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order27-declaration_order27-expected_order27]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order28-declaration_order28-expected_order28]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order29-declaration_order29-expected_order29]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order3-declaration_order3-expected_order3]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order30-declaration_order30-expected_order30]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order31-declaration_order31-expected_order31]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order32-declaration_order32-expected_order32]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order33-declaration_order33-expected_order33]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order4-declaration_order4-expected_order4]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order5-declaration_order5-expected_order5]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order6-declaration_order6-expected_order6]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order7-declaration_order7-expected_order7]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order8-declaration_order8-expected_order8]", "tests/test_commands.py::test_iter_params_for_processing[invocation_order9-declaration_order9-expected_order9]", "tests/test_commands.py::test_object_propagation", "tests/test_commands.py::test_other_command_forward", "tests/test_commands.py::test_other_command_invoke", "tests/test_commands.py::test_other_command_invoke_with_defaults", "tests/test_commands.py::test_unprocessed_options", "tests/test_compat.py::test_is_jupyter_kernel_output", "tests/test_context.py::test_close_before_exit", "tests/test_context.py::test_close_before_pop", "tests/test_context.py::test_context_meta", "tests/test_context.py::test_context_pushing", "tests/test_context.py::test_ensure_context_objects", "tests/test_context.py::test_exit_not_standalone", "tests/test_context.py::test_get_context_objects", "tests/test_context.py::test_get_context_objects_missing", "tests/test_context.py::test_get_context_objects_no_ensuring", "tests/test_context.py::test_global_context_object", "tests/test_context.py::test_make_pass_decorator_args", "tests/test_context.py::test_make_pass_meta_decorator", "tests/test_context.py::test_make_pass_meta_decorator_doc", "tests/test_context.py::test_multi_enter", "tests/test_context.py::test_multiple_eager_callbacks[eagerness_precedence]", "tests/test_context.py::test_multiple_eager_callbacks[natural_order]", "tests/test_context.py::test_no_state_leaks", "tests/test_context.py::test_parameter_source[commandline long]", "tests/test_context.py::test_parameter_source[commandline short]", "tests/test_context.py::test_parameter_source[default]", "tests/test_context.py::test_parameter_source[default_map]", "tests/test_context.py::test_parameter_source[environment auto]", "tests/test_context.py::test_parameter_source[environment manual]", "tests/test_context.py::test_pass_obj", "tests/test_context.py::test_propagate_opt_prefixes", "tests/test_context.py::test_propagate_show_default_setting", "tests/test_context.py::test_with_resource", "tests/test_custom_classes.py::test_command_context_class", "tests/test_custom_classes.py::test_context_formatter_class", "tests/test_custom_classes.py::test_context_invoke_type", "tests/test_custom_classes.py::test_group_command_class", "tests/test_custom_classes.py::test_group_group_class", "tests/test_custom_classes.py::test_group_group_class_self", "tests/test_defaults.py::test_basic_defaults", "tests/test_defaults.py::test_flag_default_map", "tests/test_defaults.py::test_multiple_defaults", "tests/test_defaults.py::test_multiple_flag_default", "tests/test_defaults.py::test_nargs_plus_multiple", "tests/test_formatting.py::test_basic_functionality", "tests/test_formatting.py::test_formatting_custom_type_metavar", "tests/test_formatting.py::test_formatting_empty_help_lines", "tests/test_formatting.py::test_formatting_usage_custom_help", "tests/test_formatting.py::test_formatting_usage_error", "tests/test_formatting.py::test_formatting_usage_error_metavar_bad_arg", "tests/test_formatting.py::test_formatting_usage_error_metavar_missing_arg", "tests/test_formatting.py::test_formatting_usage_error_nested", "tests/test_formatting.py::test_formatting_usage_error_no_help", "tests/test_formatting.py::test_formatting_with_options_metavar_empty", "tests/test_formatting.py::test_global_show_default", "tests/test_formatting.py::test_help_formatter_write_text", "tests/test_formatting.py::test_removing_multiline_marker", "tests/test_formatting.py::test_truncating_docstring", "tests/test_formatting.py::test_truncating_docstring_no_help", "tests/test_formatting.py::test_wrapping_long_command_name", "tests/test_formatting.py::test_wrapping_long_options_strings", "tests/test_imports.py::test_light_imports", "tests/test_info_dict.py::test_command[Command]", "tests/test_info_dict.py::test_command[Group]", "tests/test_info_dict.py::test_command[Nested Group]", "tests/test_info_dict.py::test_context", "tests/test_info_dict.py::test_parameter[Argument]", "tests/test_info_dict.py::test_parameter[Bool ParamType]", "tests/test_info_dict.py::test_parameter[Choice ParamType]", "tests/test_info_dict.py::test_parameter[DateTime ParamType]", "tests/test_info_dict.py::test_parameter[FLOAT ParamType]", "tests/test_info_dict.py::test_parameter[File ParamType]", "tests/test_info_dict.py::test_parameter[Flag Option]", "tests/test_info_dict.py::test_parameter[FloatRange ParamType]", "tests/test_info_dict.py::test_parameter[Func ParamType]", "tests/test_info_dict.py::test_parameter[INT ParamType]", "tests/test_info_dict.py::test_parameter[IntRange ParamType]", "tests/test_info_dict.py::test_parameter[Option]", "tests/test_info_dict.py::test_parameter[Path ParamType]", "tests/test_info_dict.py::test_parameter[STRING ParamType]", "tests/test_info_dict.py::test_parameter[Tuple ParamType]", "tests/test_info_dict.py::test_parameter[UNPROCESSED ParamType]", "tests/test_info_dict.py::test_parameter[UUID ParamType]", "tests/test_info_dict.py::test_paramtype_no_name", "tests/test_normalization.py::test_choice_normalization", "tests/test_normalization.py::test_command_normalization", "tests/test_normalization.py::test_option_normalization", "tests/test_options.py::test_aliases_for_flags", "tests/test_options.py::test_argument_custom_class", "tests/test_options.py::test_bool_flag_auto_detection[bool flag_value]", "tests/test_options.py::test_bool_flag_auto_detection[bool non-flag [False]]", "tests/test_options.py::test_bool_flag_auto_detection[bool non-flag [None]]", "tests/test_options.py::test_bool_flag_auto_detection[bool non-flag [True]]", "tests/test_options.py::test_bool_flag_auto_detection[int option]", "tests/test_options.py::test_bool_flag_auto_detection[is_flag=True]", "tests/test_options.py::test_bool_flag_auto_detection[non-bool flag_value]", "tests/test_options.py::test_bool_flag_auto_detection[secondary option [implicit flag]]", "tests/test_options.py::test_bool_flag_with_type", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-00]", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-01]", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-10]", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-11]", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-None]", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-randomstring]", "tests/test_options.py::test_boolean_envvar_bad_values[SHOUT-tr ue]", "tests/test_options.py::test_boolean_envvar_normalization[  SHOUT  -False-False]", "tests/test_options.py::test_boolean_envvar_normalization[  SHOUT  -True-False]", "tests/test_options.py::test_boolean_envvar_normalization[NO_SHOUT-False-False]", "tests/test_options.py::test_boolean_envvar_normalization[NO_SHOUT-True-False]", "tests/test_options.py::test_boolean_envvar_normalization[RANDOM-True-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT  -False-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT  -True-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-       -False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT- -False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT- false -False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT- false-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT- true -True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT- true-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT--False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-0-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-1-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-FALSE-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-FaLsE-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-False-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-None-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-TRUE-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-TruE-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-True-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-f-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-falsE-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-false -False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-false-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-n-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-no-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-off-False]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-on-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-t-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-truE-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-true -True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-true-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-y-True]", "tests/test_options.py::test_boolean_envvar_normalization[SHOUT-yes-True]", "tests/test_options.py::test_callback_validates_prompt", "tests/test_options.py::test_case_insensitive_choice", "tests/test_options.py::test_case_insensitive_choice_returned_exactly", "tests/test_options.py::test_choice_default_rendering[Color-Color.GREEN-GREEN]", "tests/test_options.py::test_choice_default_rendering[ColorInt-2-GREEN]", "tests/test_options.py::test_choice_default_rendering[HashType-HashType.SHA1-SHA1]", "tests/test_options.py::test_choice_default_rendering[HashType-SHA1-SHA1]", "tests/test_options.py::test_choice_default_rendering[Letter-b-B]", "tests/test_options.py::test_choice_default_rendering[Number-2-TWO]", "tests/test_options.py::test_choice_default_rendering[choices0-bar-bar]", "tests/test_options.py::test_choice_default_rendering[choices1-random-random]", "tests/test_options.py::test_choice_default_rendering[choices10-foo-foo]", "tests/test_options.py::test_choice_default_rendering[choices11-1-1]", "tests/test_options.py::test_choice_default_rendering[choices2-None-None]", "tests/test_options.py::test_choice_default_rendering[choices3-0-0]", "tests/test_options.py::test_choice_default_rendering[choices4-0.0-0.0]", "tests/test_options.py::test_choice_default_rendering[choices5-2-2]", "tests/test_options.py::test_choice_default_rendering[choices6-2-2]", "tests/test_options.py::test_choice_default_rendering[choices7-2.0-2.0]", "tests/test_options.py::test_choice_default_rendering[choices8-True-True]", "tests/test_options.py::test_choice_default_rendering[choices9-False-False]", "tests/test_options.py::test_count_default_type_help", "tests/test_options.py::test_counting", "tests/test_options.py::test_custom_validation", "tests/test_options.py::test_deprecated_prompt", "tests/test_options.py::test_deprecated_required", "tests/test_options.py::test_deprecated_usage[True]", "tests/test_options.py::test_deprecated_usage[USE B INSTEAD]", "tests/test_options.py::test_deprecated_warning[True]", "tests/test_options.py::test_deprecated_warning[USE B INSTEAD]", "tests/test_options.py::test_do_not_show_default_empty_multiple", "tests/test_options.py::test_do_not_show_no_default", "tests/test_options.py::test_duplicate_names_warning[opts_one0-opts_two0]", "tests/test_options.py::test_duplicate_names_warning[opts_one1-opts_two1]", "tests/test_options.py::test_dynamic_default_help_special_method", "tests/test_options.py::test_dynamic_default_help_text", "tests/test_options.py::test_dynamic_default_help_unset", "tests/test_options.py::test_empty_envvar[AUTO_MYPATH]", "tests/test_options.py::test_empty_envvar[MYPATH]", "tests/test_options.py::test_envvar_flag_value[ bar - bar - bar ]", "tests/test_options.py::test_envvar_flag_value[ bar -BAR-False]", "tests/test_options.py::test_envvar_flag_value[ bar -bar-False]", "tests/test_options.py::test_envvar_flag_value[BAR-BAR-BAR]", "tests/test_options.py::test_envvar_flag_value[BAR-bar-False]", "tests/test_options.py::test_envvar_flag_value[bar- bar -False]", "tests/test_options.py::test_envvar_flag_value[bar--False]", "tests/test_options.py::test_envvar_flag_value[bar-0-False]", "tests/test_options.py::test_envvar_flag_value[bar-1-bar]", "tests/test_options.py::test_envvar_flag_value[bar-BAR-False]", "tests/test_options.py::test_envvar_flag_value[bar-False-False]", "tests/test_options.py::test_envvar_flag_value[bar-True-bar]", "tests/test_options.py::test_envvar_flag_value[bar-bar random-False]", "tests/test_options.py::test_envvar_flag_value[bar-bar-bar]", "tests/test_options.py::test_envvar_flag_value[bar-f-False]", "tests/test_options.py::test_envvar_flag_value[bar-random bar-False]", "tests/test_options.py::test_envvar_flag_value[bar-random-False]", "tests/test_options.py::test_envvar_flag_value[bar-t-bar]", "tests/test_options.py::test_file_type_help_default", "tests/test_options.py::test_flag_duplicate_names", "tests/test_options.py::test_flag_value_and_default[opt_params0-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params1-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params10-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params11-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params12-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params13-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params14-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params15-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params16-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params17-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params18-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params19-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params2-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params20-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params21-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params22-False-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params23-True-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params24-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params25-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params26-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params27-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params28-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params29-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params3-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params30-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params31-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params32-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params33-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params34-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params35-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params36-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params37-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params38-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params39-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params4-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params40-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params41-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params42-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params43-True-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params44-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params45-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params46-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params47-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params48-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params49-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params5-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params50-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params51-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params52-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params53-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params54-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params55-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params56-False-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params57-False-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params58-False-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params59-False-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params6-False-True]", "tests/test_options.py::test_flag_value_and_default[opt_params60-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params61-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params62-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params63-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params64-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params65-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params66-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params67-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params68-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params69-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params7-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params70-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params71-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params72-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params73-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params74-True-bar]", "tests/test_options.py::test_flag_value_and_default[opt_params75-True-foo]", "tests/test_options.py::test_flag_value_and_default[opt_params76-True-True]", "tests/test_options.py::test_flag_value_and_default[opt_params77-True-False]", "tests/test_options.py::test_flag_value_and_default[opt_params8-False-False]", "tests/test_options.py::test_flag_value_and_default[opt_params9-True-True]", "tests/test_options.py::test_help_option_custom_names_and_class[name_specs0-  -h, --help  Show this message and exit.\\n-False]", "tests/test_options.py::test_help_option_custom_names_and_class[name_specs0-  -h, --help  Show this message and exit.\\n-True]", "tests/test_options.py::test_help_option_custom_names_and_class[name_specs1-  -h      Show this message and exit.\\n  --help  Show this message and exit.\\n-False]", "tests/test_options.py::test_help_option_custom_names_and_class[name_specs1-  -h      Show this message and exit.\\n  --help  Show this message and exit.\\n-True]", "tests/test_options.py::test_help_option_custom_names_and_class[name_specs2-  --help  Show this message and exit.\\n-False]", "tests/test_options.py::test_help_option_custom_names_and_class[name_specs2-  --help  Show this message and exit.\\n-True]", "tests/test_options.py::test_hide_false_default_boolean_flag_value[False]", "tests/test_options.py::test_hide_false_default_boolean_flag_value[None]", "tests/test_options.py::test_init_bad_default_list[False-2-default1]", "tests/test_options.py::test_init_bad_default_list[True-1-1]", "tests/test_options.py::test_init_bad_default_list[True-2-default2]", "tests/test_options.py::test_init_good_default_list[False-2-default2]", "tests/test_options.py::test_init_good_default_list[True-1-default0]", "tests/test_options.py::test_init_good_default_list[True-1-default1]", "tests/test_options.py::test_init_good_default_list[True-2-default3]", "tests/test_options.py::test_init_good_default_list[True-2-default4]", "tests/test_options.py::test_intrange_default_help_text[type0-1<=x<=32]", "tests/test_options.py::test_intrange_default_help_text[type1-1<x<32]", "tests/test_options.py::test_intrange_default_help_text[type2-x>=1]", "tests/test_options.py::test_intrange_default_help_text[type3-x<=32]", "tests/test_options.py::test_invalid_flag_combinations[kwargs0-'count' is not valid with 'multiple'.]", "tests/test_options.py::test_invalid_flag_combinations[kwargs1-'count' is not valid with 'is_flag'.]", "tests/test_options.py::test_invalid_flag_definition[opts0]", "tests/test_options.py::test_invalid_flag_definition[opts1]", "tests/test_options.py::test_invalid_flag_definition[opts2]", "tests/test_options.py::test_invalid_flag_definition[opts3]", "tests/test_options.py::test_invalid_nargs", "tests/test_options.py::test_invalid_option", "tests/test_options.py::test_legacy_options", "tests/test_options.py::test_missing_choice", "tests/test_options.py::test_missing_envvar", "tests/test_options.py::test_missing_option_string_cast", "tests/test_options.py::test_missing_required_flag", "tests/test_options.py::test_multiple_default_composite_type", "tests/test_options.py::test_multiple_default_help", "tests/test_options.py::test_multiple_default_type", "tests/test_options.py::test_multiple_envvar", "tests/test_options.py::test_multiple_option_with_optional_value", "tests/test_options.py::test_multiple_required", "tests/test_options.py::test_nargs_envvar", "tests/test_options.py::test_nargs_tup_composite_mult", "tests/test_options.py::test_non_flag_with_non_negatable_default", "tests/test_options.py::test_option_custom_class", "tests/test_options.py::test_option_custom_class_reusable", "tests/test_options.py::test_option_help_preserve_paragraphs", "tests/test_options.py::test_option_names[option_args0-aggressive]", "tests/test_options.py::test_option_names[option_args1-first]", "tests/test_options.py::test_option_names[option_args2-apple]", "tests/test_options.py::test_option_names[option_args3-cantaloupe]", "tests/test_options.py::test_option_names[option_args4-a]", "tests/test_options.py::test_option_names[option_args5-c]", "tests/test_options.py::test_option_names[option_args6-apple]", "tests/test_options.py::test_option_names[option_args7-cantaloupe]", "tests/test_options.py::test_option_names[option_args8-_from]", "tests/test_options.py::test_option_names[option_args9-_ret]", "tests/test_options.py::test_option_with_optional_value[None-expect0]", "tests/test_options.py::test_option_with_optional_value[args1-expect1]", "tests/test_options.py::test_option_with_optional_value[args10-expect10]", "tests/test_options.py::test_option_with_optional_value[args11-expect11]", "tests/test_options.py::test_option_with_optional_value[args12-expect12]", "tests/test_options.py::test_option_with_optional_value[args13-expect13]", "tests/test_options.py::test_option_with_optional_value[args2-expect2]", "tests/test_options.py::test_option_with_optional_value[args3-expect3]", "tests/test_options.py::test_option_with_optional_value[args4-expect4]", "tests/test_options.py::test_option_with_optional_value[args5-expect5]", "tests/test_options.py::test_option_with_optional_value[args6-expect6]", "tests/test_options.py::test_option_with_optional_value[args7-expect7]", "tests/test_options.py::test_option_with_optional_value[args8-expect8]", "tests/test_options.py::test_option_with_optional_value[args9-expect9]", "tests/test_options.py::test_parse_multiple_default_composite_type", "tests/test_options.py::test_prefixes", "tests/test_options.py::test_show_default_boolean_flag_name[False-no-cache]", "tests/test_options.py::test_show_default_boolean_flag_name[True-cache]", "tests/test_options.py::test_show_default_default_map", "tests/test_options.py::test_show_default_precedence[False-False-extra_value4-False]", "tests/test_options.py::test_show_default_precedence[False-None-extra_value3-False]", "tests/test_options.py::test_show_default_precedence[False-True-extra_value5-True]", "tests/test_options.py::test_show_default_precedence[False-one-extra_value9-True]", "tests/test_options.py::test_show_default_precedence[None-False-extra_value1-False]", "tests/test_options.py::test_show_default_precedence[None-None-extra_value0-False]", "tests/test_options.py::test_show_default_precedence[None-True-extra_value2-True]", "tests/test_options.py::test_show_default_precedence[True-False-extra_value7-False]", "tests/test_options.py::test_show_default_precedence[True-None-extra_value6-True]", "tests/test_options.py::test_show_default_precedence[True-True-extra_value8-True]", "tests/test_options.py::test_show_default_string", "tests/test_options.py::test_show_default_with_empty_string", "tests/test_options.py::test_show_envvar", "tests/test_options.py::test_show_envvar_auto_prefix", "tests/test_options.py::test_show_envvar_auto_prefix_dash_in_command", "tests/test_options.py::test_show_true_default_boolean_flag_value", "tests/test_options.py::test_suggest_possible_options[--bounds-(Possible options: --bound, --count)]", "tests/test_options.py::test_suggest_possible_options[--bount-(Possible options: --bound, --count)]", "tests/test_options.py::test_suggest_possible_options[--cat-Did you mean --count?]", "tests/test_options.py::test_toupper_envvar_prefix", "tests/test_options.py::test_type_from_flag_value", "tests/test_options.py::test_unknown_options[--foo]", "tests/test_options.py::test_unknown_options[-f]", "tests/test_options.py::test_usage_show_choices[bool choices]", "tests/test_options.py::test_usage_show_choices[enum choices]", "tests/test_options.py::test_usage_show_choices[flag enum choices]", "tests/test_options.py::test_usage_show_choices[float choices]", "tests/test_options.py::test_usage_show_choices[int choices]", "tests/test_options.py::test_usage_show_choices[int enum choices]", "tests/test_options.py::test_usage_show_choices[int flag enum choices]", "tests/test_options.py::test_usage_show_choices[str enum choices]", "tests/test_options.py::test_usage_show_choices[text choices]", "tests/test_options.py::test_usage_show_choices[text/int choices]", "tests/test_options.py::test_winstyle_options", "tests/test_parser.py::test_parser_collects_prefixes", "tests/test_parser.py::test_parser_default_prefixes", "tests/test_parser.py::test_split_arg_string[cli 'my file'-expect2]", "tests/test_parser.py::test_split_arg_string[cli 'my file-expect1]", "tests/test_parser.py::test_split_arg_string[cli a b c-expect0]", "tests/test_parser.py::test_split_arg_string[cli my\\\\ file-expect4]", "tests/test_parser.py::test_split_arg_string[cli my\\\\-expect3]", "tests/test_shell_completion.py::test_absolute_path", "tests/test_shell_completion.py::test_add_completion_class", "tests/test_shell_completion.py::test_add_completion_class_decorator", "tests/test_shell_completion.py::test_add_completion_class_with_name", "tests/test_shell_completion.py::test_add_different_name", "tests/test_shell_completion.py::test_argument_default", "tests/test_shell_completion.py::test_argument_nargs", "tests/test_shell_completion.py::test_argument_order", "tests/test_shell_completion.py::test_chained", "tests/test_shell_completion.py::test_choice_case_sensitive[False-expect0]", "tests/test_shell_completion.py::test_choice_case_sensitive[True-expect1]", "tests/test_shell_completion.py::test_choice_conflicting_prefix", "tests/test_shell_completion.py::test_choice_special_characters", "tests/test_shell_completion.py::test_command", "tests/test_shell_completion.py::test_completion_item_data", "tests/test_shell_completion.py::test_context_settings", "tests/test_shell_completion.py::test_double_dash", "tests/test_shell_completion.py::test_files_closed", "tests/test_shell_completion.py::test_full_complete[bash-env0-plain,a\\nplain,b\\n]", "tests/test_shell_completion.py::test_full_complete[bash-env1-plain,b\\n]", "tests/test_shell_completion.py::test_full_complete[fish-env4-plain,a\\nplain,b\\tbee\\n]", "tests/test_shell_completion.py::test_full_complete[fish-env5-plain,b\\tbee\\n]", "tests/test_shell_completion.py::test_full_complete[zsh-env2-plain\\na\\n_\\nplain\\nb\\nbee\\n]", "tests/test_shell_completion.py::test_full_complete[zsh-env3-plain\\nb\\nbee\\n]", "tests/test_shell_completion.py::test_full_source[bash]", "tests/test_shell_completion.py::test_full_source[fish]", "tests/test_shell_completion.py::test_full_source[zsh]", "tests/test_shell_completion.py::test_group", "tests/test_shell_completion.py::test_group_command_same_option", "tests/test_shell_completion.py::test_help_option", "tests/test_shell_completion.py::test_hidden", "tests/test_shell_completion.py::test_nested_group[args0--expect0]", "tests/test_shell_completion.py::test_nested_group[args1--expect1]", "tests/test_shell_completion.py::test_nested_group[args2--expect2]", "tests/test_shell_completion.py::test_nested_group[args3---expect3]", "tests/test_shell_completion.py::test_nested_group[args4--expect4]", "tests/test_shell_completion.py::test_nested_group[args5---expect5]", "tests/test_shell_completion.py::test_option_count", "tests/test_shell_completion.py::test_option_custom", "tests/test_shell_completion.py::test_option_flag", "tests/test_shell_completion.py::test_option_multiple", "tests/test_shell_completion.py::test_option_nargs", "tests/test_shell_completion.py::test_option_optional", "tests/test_shell_completion.py::test_path_types[type0-file]", "tests/test_shell_completion.py::test_path_types[type1-file]", "tests/test_shell_completion.py::test_path_types[type2-dir]", "tests/test_shell_completion.py::test_type_choice", "tests/test_shell_completion.py::test_zsh_full_complete_with_colons[env0-plain\\na\\n_\\nplain\\nb\\nbee\\nplain\\nc\\\\:d\\ncee:dee\\nplain\\nc:e\\n_\\n]", "tests/test_shell_completion.py::test_zsh_full_complete_with_colons[env1-plain\\nc\\\\:d\\ncee:dee\\nplain\\nc:e\\n_\\n]", "tests/test_shell_completion.py::test_zsh_full_complete_with_colons[env2-plain\\nc\\\\:d\\ncee:dee\\nplain\\nc:e\\n_\\n]", "tests/test_termui.py::test_choices_list_in_prompt", "tests/test_termui.py::test_confirmation_prompt[Confirm Password-password\\npassword\\n-None-password]", "tests/test_termui.py::test_confirmation_prompt[False-None-None-None]", "tests/test_termui.py::test_confirmation_prompt[True-\\n\\n--]", "tests/test_termui.py::test_confirmation_prompt[True-password\\npassword-None-password]", "tests/test_termui.py::test_edit", "tests/test_termui.py::test_false_show_default_cause_no_default_display_in_prompt", "tests/test_termui.py::test_fast_edit", "tests/test_termui.py::test_file_prompt_default_format[file_kwargs0]", "tests/test_termui.py::test_file_prompt_default_format[file_kwargs1]", "tests/test_termui.py::test_file_prompt_default_format[file_kwargs2]", "tests/test_termui.py::test_getchar_special_key_windows[\\x00-a]", "tests/test_termui.py::test_getchar_special_key_windows[\\x00-b]", "tests/test_termui.py::test_getchar_special_key_windows[\\xe0-c]", "tests/test_termui.py::test_getchar_windows[False- ]", "tests/test_termui.py::test_getchar_windows[False-H]", "tests/test_termui.py::test_getchar_windows[False-\\u5b57]", "tests/test_termui.py::test_getchar_windows[False-\\xc0]", "tests/test_termui.py::test_getchar_windows[False-\\xe0H]", "tests/test_termui.py::test_getchar_windows[False-\\xe0R]", "tests/test_termui.py::test_getchar_windows[False-\\xe9]", "tests/test_termui.py::test_getchar_windows[False-h]", "tests/test_termui.py::test_getchar_windows[True- ]", "tests/test_termui.py::test_getchar_windows[True-H]", "tests/test_termui.py::test_getchar_windows[True-\\u5b57]", "tests/test_termui.py::test_getchar_windows[True-\\xc0]", "tests/test_termui.py::test_getchar_windows[True-\\xe0H]", "tests/test_termui.py::test_getchar_windows[True-\\xe0R]", "tests/test_termui.py::test_getchar_windows[True-\\xe9]", "tests/test_termui.py::test_getchar_windows[True-h]", "tests/test_termui.py::test_getchar_windows_exceptions[\\x03-KeyboardInterrupt]", "tests/test_termui.py::test_getchar_windows_exceptions[\\x1a-EOFError]", "tests/test_termui.py::test_progress_bar_update_min_steps", "tests/test_termui.py::test_progressbar_eta[False-5]", "tests/test_termui.py::test_progressbar_eta[True-0]", "tests/test_termui.py::test_progressbar_format_bar[0-True-8-0-########]", "tests/test_termui.py::test_progressbar_format_bar[8-False-7-0-#######-]", "tests/test_termui.py::test_progressbar_format_eta[0-00:00:00]", "tests/test_termui.py::test_progressbar_format_eta[30-00:00:30]", "tests/test_termui.py::test_progressbar_format_eta[90-00:01:30]", "tests/test_termui.py::test_progressbar_format_eta[900-00:15:00]", "tests/test_termui.py::test_progressbar_format_eta[9000-02:30:00]", "tests/test_termui.py::test_progressbar_format_eta[99999999999-1157407d 09:46:39]", "tests/test_termui.py::test_progressbar_format_eta[None-]", "tests/test_termui.py::test_progressbar_format_pos[-1-1]", "tests/test_termui.py::test_progressbar_format_pos[0-5]", "tests/test_termui.py::test_progressbar_format_pos[4-0]", "tests/test_termui.py::test_progressbar_format_pos[5-5]", "tests/test_termui.py::test_progressbar_format_pos[6-5]", "tests/test_termui.py::test_progressbar_format_progress_line[0-False-False-0-  [--------]0]", "tests/test_termui.py::test_progressbar_format_progress_line[0-False-False-0-  [--------]1]", "tests/test_termui.py::test_progressbar_format_progress_line[0-False-True-0-  [--------]  0/0]", "tests/test_termui.py::test_progressbar_format_progress_line[0-True-True-0-  [--------]  0/0    0%]", "tests/test_termui.py::test_progressbar_format_progress_line[8-True-True-8-  [########]  8/8  100%]", "tests/test_termui.py::test_progressbar_format_progress_line_with_show_func[None]", "tests/test_termui.py::test_progressbar_format_progress_line_with_show_func[test]", "tests/test_termui.py::test_progressbar_hidden_manual", "tests/test_termui.py::test_progressbar_init_exceptions", "tests/test_termui.py::test_progressbar_is_iterator", "tests/test_termui.py::test_progressbar_item_show_func", "tests/test_termui.py::test_progressbar_iter_outside_with_exceptions", "tests/test_termui.py::test_progressbar_length_hint", "tests/test_termui.py::test_progressbar_no_tty", "tests/test_termui.py::test_progressbar_strip_regression", "tests/test_termui.py::test_progressbar_time_per_iteration[avg0-0.0]", "tests/test_termui.py::test_progressbar_time_per_iteration[avg1-2.5]", "tests/test_termui.py::test_progressbar_update", "tests/test_termui.py::test_progressbar_update_with_item_show_func", "tests/test_termui.py::test_progressbar_yields_all_items", "tests/test_termui.py::test_prompt_required_false[long join value]", "tests/test_termui.py::test_prompt_required_false[long no value]", "tests/test_termui.py::test_prompt_required_false[long sep value]", "tests/test_termui.py::test_prompt_required_false[no flag]", "tests/test_termui.py::test_prompt_required_false[no value opt]", "tests/test_termui.py::test_prompt_required_false[short join value]", "tests/test_termui.py::test_prompt_required_false[short no value]", "tests/test_termui.py::test_prompt_required_false[short sep value]", "tests/test_termui.py::test_prompt_required_with_required[False-True-None-prompt]", "tests/test_termui.py::test_prompt_required_with_required[False-True-args3-prompt]", "tests/test_termui.py::test_prompt_required_with_required[True-False-None-prompt]", "tests/test_termui.py::test_prompt_required_with_required[True-False-args1-Option '-v' requires an argument.]", "tests/test_termui.py::test_secho", "tests/test_termui.py::test_secho_non_text[123-\\x1b[45m123\\x1b[0m]", "tests/test_termui.py::test_secho_non_text[test-test]", "tests/test_testing.py::test_args[--foo \"one two\"-one two\\n]", "tests/test_testing.py::test_args[-bar\\n]", "tests/test_testing.py::test_args[None-bar\\n]", "tests/test_testing.py::test_args[args1-bar\\n]", "tests/test_testing.py::test_args[args3-one two\\n]", "tests/test_testing.py::test_catch_exceptions", "tests/test_testing.py::test_catch_exceptions_cli_runner", "tests/test_testing.py::test_command_standalone_mode_returns_value", "tests/test_testing.py::test_echo_stdin_prompts", "tests/test_testing.py::test_echo_stdin_stream", "tests/test_testing.py::test_env", "tests/test_testing.py::test_exit_code_and_output_from_sys_exit", "tests/test_testing.py::test_file_stdin_attrs", "tests/test_testing.py::test_getchar", "tests/test_testing.py::test_isolated_runner", "tests/test_testing.py::test_isolated_runner_custom_tempdir", "tests/test_testing.py::test_isolation_flushes_unflushed_stderr", "tests/test_testing.py::test_isolation_stderr_errors", "tests/test_testing.py::test_prompts", "tests/test_testing.py::test_runner", "tests/test_testing.py::test_runner_with_stream", "tests/test_testing.py::test_setting_prog_name_in_extra", "tests/test_testing.py::test_stderr", "tests/test_testing.py::test_with_color", "tests/test_testing.py::test_with_color_but_pause_not_blocking", "tests/test_testing.py::test_with_color_errors", "tests/test_types.py::test_cast_multi_default[-1-None-None-expect6]", "tests/test_types.py::test_cast_multi_default[2-False-None-None]", "tests/test_types.py::test_cast_multi_default[2-False-default1-expect1]", "tests/test_types.py::test_cast_multi_default[2-True-None-expect4]", "tests/test_types.py::test_cast_multi_default[2-True-default5-expect5]", "tests/test_types.py::test_cast_multi_default[None-True-None-expect2]", "tests/test_types.py::test_cast_multi_default[None-True-default3-expect3]", "tests/test_types.py::test_choice_get_invalid_choice_message", "tests/test_types.py::test_file_error_surrogates", "tests/test_types.py::test_file_surrogates[type0]", "tests/test_types.py::test_file_surrogates[type1]", "tests/test_types.py::test_float_range_no_clamp_open", "tests/test_types.py::test_invalid_path_with_esc_sequence", "tests/test_types.py::test_path_resolve_symlink", "tests/test_types.py::test_path_surrogates", "tests/test_types.py::test_path_type[None-a/b/c.txt]", "tests/test_types.py::test_path_type[Path-expect3]", "tests/test_types.py::test_path_type[bytes-a/b/c.txt]", "tests/test_types.py::test_path_type[str-a/b/c.txt]", "tests/test_types.py::test_range[type0-3-3]", "tests/test_types.py::test_range[type1-5-5]", "tests/test_types.py::test_range[type10-0.51-0.51]", "tests/test_types.py::test_range[type11-1.49-1.49]", "tests/test_types.py::test_range[type12--0.0-0.5]", "tests/test_types.py::test_range[type13-inf-1.5]", "tests/test_types.py::test_range[type2-100-100]", "tests/test_types.py::test_range[type3-5-5]", "tests/test_types.py::test_range[type4--100--100]", "tests/test_types.py::test_range[type5--1-0]", "tests/test_types.py::test_range[type6-6-5]", "tests/test_types.py::test_range[type7-0-1]", "tests/test_types.py::test_range[type8-5-4]", "tests/test_types.py::test_range[type9-1.2-1.2]", "tests/test_types.py::test_range_fail[type0-6-6 is not in the range 0<=x<=5.]", "tests/test_types.py::test_range_fail[type1-4-4 is not in the range x>=5.]", "tests/test_types.py::test_range_fail[type2-6-6 is not in the range x<=5.]", "tests/test_types.py::test_range_fail[type3-0-0<x<=5]", "tests/test_types.py::test_range_fail[type4-5-0<=x<5]", "tests/test_types.py::test_range_fail[type5-0.5-x>0.5]", "tests/test_types.py::test_range_fail[type6-1.5-x<1.5]", "tests/test_utils.py::test_confirm_repeat", "tests/test_utils.py::test_detect_program_name[/foo/bar/example.py-None-example.py]", "tests/test_utils.py::test_detect_program_name[example--example]", "tests/test_utils.py::test_detect_program_name[example-None-example]", "tests/test_utils.py::test_detect_program_name[example.py-None-example.py]", "tests/test_utils.py::test_detect_program_name[example/__main__.py-example-python -m example]", "tests/test_utils.py::test_detect_program_name[example/cli.py-example-python -m example.cli]", "tests/test_utils.py::test_echo", "tests/test_utils.py::test_echo_color_flag", "tests/test_utils.py::test_echo_custom_file", "tests/test_utils.py::test_echo_no_streams", "tests/test_utils.py::test_echo_via_pager[test0- cat ]", "tests/test_utils.py::test_echo_via_pager[test0- less ]", "tests/test_utils.py::test_echo_via_pager[test0- less]", "tests/test_utils.py::test_echo_via_pager[test0-cat ]", "tests/test_utils.py::test_echo_via_pager[test0-cat]", "tests/test_utils.py::test_echo_via_pager[test0-less]", "tests/test_utils.py::test_echo_via_pager[test1- cat ]", "tests/test_utils.py::test_echo_via_pager[test1- less ]", "tests/test_utils.py::test_echo_via_pager[test1- less]", "tests/test_utils.py::test_echo_via_pager[test1-cat ]", "tests/test_utils.py::test_echo_via_pager[test1-cat]", "tests/test_utils.py::test_echo_via_pager[test1-less]", "tests/test_utils.py::test_echo_via_pager[test2- cat ]", "tests/test_utils.py::test_echo_via_pager[test2- less ]", "tests/test_utils.py::test_echo_via_pager[test2- less]", "tests/test_utils.py::test_echo_via_pager[test2-cat ]", "tests/test_utils.py::test_echo_via_pager[test2-cat]", "tests/test_utils.py::test_echo_via_pager[test2-less]", "tests/test_utils.py::test_echo_via_pager[test3- cat ]", "tests/test_utils.py::test_echo_via_pager[test3- less ]", "tests/test_utils.py::test_echo_via_pager[test3- less]", "tests/test_utils.py::test_echo_via_pager[test3-cat ]", "tests/test_utils.py::test_echo_via_pager[test3-cat]", "tests/test_utils.py::test_echo_via_pager[test3-less]", "tests/test_utils.py::test_echo_via_pager[test4- cat ]", "tests/test_utils.py::test_echo_via_pager[test4- less ]", "tests/test_utils.py::test_echo_via_pager[test4- less]", "tests/test_utils.py::test_echo_via_pager[test4-cat ]", "tests/test_utils.py::test_echo_via_pager[test4-cat]", "tests/test_utils.py::test_echo_via_pager[test4-less]", "tests/test_utils.py::test_echo_via_pager[test5- cat ]", "tests/test_utils.py::test_echo_via_pager[test5- less ]", "tests/test_utils.py::test_echo_via_pager[test5- less]", "tests/test_utils.py::test_echo_via_pager[test5-cat ]", "tests/test_utils.py::test_echo_via_pager[test5-cat]", "tests/test_utils.py::test_echo_via_pager[test5-less]", "tests/test_utils.py::test_echo_via_pager[test6- cat ]", "tests/test_utils.py::test_echo_via_pager[test6- less ]", "tests/test_utils.py::test_echo_via_pager[test6- less]", "tests/test_utils.py::test_echo_via_pager[test6-cat ]", "tests/test_utils.py::test_echo_via_pager[test6-cat]", "tests/test_utils.py::test_echo_via_pager[test6-less]", "tests/test_utils.py::test_echo_via_pager[test7- cat ]", "tests/test_utils.py::test_echo_via_pager[test7- less ]", "tests/test_utils.py::test_echo_via_pager[test7- less]", "tests/test_utils.py::test_echo_via_pager[test7-cat ]", "tests/test_utils.py::test_echo_via_pager[test7-cat]", "tests/test_utils.py::test_echo_via_pager[test7-less]", "tests/test_utils.py::test_echo_via_pager[test8- cat ]", "tests/test_utils.py::test_echo_via_pager[test8- less ]", "tests/test_utils.py::test_echo_via_pager[test8- less]", "tests/test_utils.py::test_echo_via_pager[test8-cat ]", "tests/test_utils.py::test_echo_via_pager[test8-cat]", "tests/test_utils.py::test_echo_via_pager[test8-less]", "tests/test_utils.py::test_echo_via_pager[test9- cat ]", "tests/test_utils.py::test_echo_via_pager[test9- less ]", "tests/test_utils.py::test_echo_via_pager[test9- less]", "tests/test_utils.py::test_echo_via_pager[test9-cat ]", "tests/test_utils.py::test_echo_via_pager[test9-cat]", "tests/test_utils.py::test_echo_via_pager[test9-less]", "tests/test_utils.py::test_echo_with_capsys", "tests/test_utils.py::test_echo_writing_to_standard_error", "tests/test_utils.py::test_expand_args", "tests/test_utils.py::test_filename_formatting", "tests/test_utils.py::test_iter_keepopenfile", "tests/test_utils.py::test_iter_lazyfile", "tests/test_utils.py::test_make_default_short_help[-empty]", "tests/test_utils.py::test_make_default_short_help[-equal length, no dot]", "tests/test_utils.py::test_make_default_short_help[-ignore dot in word]", "tests/test_utils.py::test_make_default_short_help[-length includes suffix]", "tests/test_utils.py::test_make_default_short_help[-paragraph < max]", "tests/test_utils.py::test_make_default_short_help[-sentence < max]", "tests/test_utils.py::test_make_default_short_help[-truncate]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-empty]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-equal length, no dot]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-ignore dot in word]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-length includes suffix]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-paragraph < max]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-sentence < max]", "tests/test_utils.py::test_make_default_short_help[no-wrap mark-truncate]", "tests/test_utils.py::test_open_file", "tests/test_utils.py::test_open_file_atomic_permissions_existing_file[256]", "tests/test_utils.py::test_open_file_atomic_permissions_existing_file[292]", "tests/test_utils.py::test_open_file_atomic_permissions_existing_file[384]", "tests/test_utils.py::test_open_file_atomic_permissions_existing_file[420]", "tests/test_utils.py::test_open_file_atomic_permissions_new_file", "tests/test_utils.py::test_open_file_ignore_errors_stdin", "tests/test_utils.py::test_open_file_ignore_invalid_utf8", "tests/test_utils.py::test_open_file_ignore_no_encoding", "tests/test_utils.py::test_open_file_pathlib_dash", "tests/test_utils.py::test_open_file_respects_ignore", "tests/test_utils.py::test_prompt_cast_default", "tests/test_utils.py::test_prompts", "tests/test_utils.py::test_prompts_abort", "tests/test_utils.py::test_prompts_eof", "tests/test_utils.py::test_styling[styles0-\\x1b[30mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles1-\\x1b[31mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles10-\\x1b[42mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles11-\\x1b[43mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles12-\\x1b[44mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles13-\\x1b[45mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles14-\\x1b[46mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles15-\\x1b[47mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles16-\\x1b[48;5;91mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles17-\\x1b[48;2;135;0;175mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles18-\\x1b[1mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles19-\\x1b[2mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles2-\\x1b[32mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles20-\\x1b[4mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles21-\\x1b[53mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles22-\\x1b[3mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles23-\\x1b[5mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles24-\\x1b[7mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles25-\\x1b[9mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles26-\\x1b[22mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles27-\\x1b[22mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles28-\\x1b[24mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles29-\\x1b[55mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles3-\\x1b[33mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles30-\\x1b[23mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles31-\\x1b[25mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles32-\\x1b[27mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles33-\\x1b[29mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles34-\\x1b[30mx y]", "tests/test_utils.py::test_styling[styles4-\\x1b[34mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles5-\\x1b[35mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles6-\\x1b[36mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles7-\\x1b[37mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles8-\\x1b[40mx y\\x1b[0m]", "tests/test_utils.py::test_styling[styles9-\\x1b[41mx y\\x1b[0m]", "tests/test_utils.py::test_unstyle_other_ansi[\\x1b[?25lx y\\x1b[?25h-x y]"]