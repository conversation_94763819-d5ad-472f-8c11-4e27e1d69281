name: Tests
on:
  pull_request:
    paths-ignore: ['docs/**', 'README.md']
  push:
    branches: [main, stable]
    paths-ignore: ['docs/**', 'README.md']
jobs:
  tests:
    name: ${{ matrix.name || matrix.python }}
    runs-on: ${{ matrix.os || 'ubuntu-latest' }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - {python: '3.13'}
          - {name: Windows, python: '3.13', os: windows-latest}
          - {name: Mac, python: '3.13', os: macos-latest}
          - {python: '3.12'}
          - {python: '3.11'}
          - {python: '3.10'}
          - {name: PyPy, python: 'pypy-3.11', tox: pypy3.11}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: astral-sh/setup-uv@f0ec1fc3b38f5e7cd731bb6ce540c5af426746bb # v6.1.0
        with:
          enable-cache: true
          prune-cache: false
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: ${{ matrix.python }}
      - run: uv run --locked tox run -e ${{ matrix.tox || format('py{0}', matrix.python) }}
  typing:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: astral-sh/setup-uv@f0ec1fc3b38f5e7cd731bb6ce540c5af426746bb # v6.1.0
        with:
          enable-cache: true
          prune-cache: false
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version-file: pyproject.toml
      - name: cache mypy
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ./.mypy_cache
          key: mypy|${{ hashFiles('pyproject.toml') }}
      - run: uv run --locked tox run -e typing
