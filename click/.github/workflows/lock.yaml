name: Lock inactive closed issues
# Lock closed issues that have not received any further activity for two weeks.
# This does not close open issues, only humans may do that. It is easier to
# respond to new issues with fresh examples rather than continuing discussions
# on old issues.

on:
  schedule:
    - cron: '0 0 * * *'
permissions:
  issues: write
  pull-requests: write
  discussions: write
concurrency:
  group: lock
jobs:
  lock:
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/lock-threads@1bf7ec25051fe7c00bdd17e6a7cf3d7bfb7dc771 # v5.0.1
        with:
          issue-inactive-days: 14
          pr-inactive-days: 14
          discussion-inactive-days: 14
