name: pre-commit
on:
  pull_request:
  push:
    branches: [main, stable]
jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: astral-sh/setup-uv@f0ec1fc3b38f5e7cd731bb6ce540c5af426746bb # v6.1.0
        with:
          enable-cache: true
          prune-cache: false
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        id: setup-python
        with:
          python-version-file: pyproject.toml
      - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/.cache/pre-commit
          key: pre-commit|${{ hashFiles('pyproject.toml', '.pre-commit-config.yaml') }}
      - run: uv run --locked --group pre-commit pre-commit run --show-diff-on-failure --color=always --all-files
      - uses: pre-commit-ci/lite-action@5d6cc0eb514c891a40562a58a8e71576c5c7fb43 # v1.1.0
        if: ${{ !cancelled() }}
